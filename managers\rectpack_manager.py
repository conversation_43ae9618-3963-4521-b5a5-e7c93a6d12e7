#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法管理器
负责管理和优化RectPack算法的执行，确保在生产环境下的稳定性和高效性

主要职责：
1. 管理RectPack算法的配置和参数
2. 优化算法性能和画布利用率
3. 处理算法执行过程中的错误和异常
4. 提供算法状态监控和反馈
5. 确保与Photoshop的稳定集成
"""

import logging
import time
from typing import Dict, List, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal

# 导入核心模块
from core.rectpack_arranger import RectPackArranger
from core.unified_image_arranger import UnifiedImageArranger
from utils.memory_manager import MemoryManager

# 配置日志
log = logging.getLogger(__name__)


class RectPackManager(QObject):
    """RectPack算法管理器

    负责管理RectPack算法的整个生命周期，包括：
    - 算法初始化和配置
    - 性能监控和优化
    - 错误处理和恢复
    - 与Photoshop的集成管理
    """

    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新信号
    status_changed = pyqtSignal(str)    # 状态变化信号
    error_occurred = pyqtSignal(str)    # 错误发生信号
    utilization_updated = pyqtSignal(float)  # 利用率更新信号

    def __init__(self, config_manager=None):
        """初始化RectPack管理器

        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        self.config_manager = config_manager
        self.memory_manager = MemoryManager()

        # 算法实例
        self.rectpack_arranger = None
        self.unified_arranger = None

        # 性能监控
        self.start_time = None
        self.last_utilization = 0.0
        self.processed_images = 0
        self.total_images = 0

        # 错误处理
        self.error_count = 0
        self.max_errors = 10
        self.retry_count = 0
        self.max_retries = 3

        # 状态管理
        self.is_running = False
        self.is_paused = False
        self.current_operation = ""

        log.info("RectPack管理器初始化完成")

    def initialize_algorithm(self, canvas_width_m: float, max_height_cm: float,
                           image_spacing_cm: float, ppi: int = 72) -> bool:
        """初始化RectPack算法

        Args:
            canvas_width_m: 画布宽度（米）
            max_height_cm: 最大高度（厘米）
            image_spacing_cm: 图片间距（厘米）
            ppi: 分辨率（像素/英寸）

        Returns:
            bool: 是否初始化成功
        """
        try:
            self.status_changed.emit("正在初始化RectPack算法...")

            # 转换单位为像素
            canvas_width_px = int(canvas_width_m * 100 / 2.54 * ppi)
            max_height_px = int(max_height_cm / 2.54 * ppi)
            image_spacing_px = int(image_spacing_cm / 2.54 * ppi)

            # 创建RectPack排列器（不传递log_signal，避免连接问题）
            self.rectpack_arranger = RectPackArranger(
                container_width=canvas_width_px,
                image_spacing=image_spacing_px,
                max_height=max_height_px,
                log_signal=None  # 不传递log_signal，避免连接问题
            )

            # 创建统一排列器
            self.unified_arranger = UnifiedImageArranger(log_signal=self._handle_algorithm_log)

            # 初始化统一排列器
            self.unified_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=image_spacing_px,
                ppi=ppi
            )

            # 不再尝试连接RectPackArranger的log_signal，因为它可能为None或不是信号对象

            log.info(f"RectPack算法初始化成功: {canvas_width_px}x{max_height_px}px, 间距={image_spacing_px}px")
            self.status_changed.emit("RectPack算法初始化成功")
            return True

        except Exception as e:
            error_msg = f"RectPack算法初始化失败: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def optimize_algorithm_parameters(self) -> Dict[str, Any]:
        """优化算法参数以获得最佳性能

        Returns:
            Dict[str, Any]: 优化后的参数配置
        """
        try:
            self.status_changed.emit("正在优化算法参数...")

            # 获取当前配置
            rectpack_settings = self.config_manager.get_rectpack_settings() if self.config_manager else {}

            # 默认优化参数
            optimized_params = {
                'pack_algo': rectpack_settings.get('pack_algo', 0),  # Best Short Side Fit
                'sort_algo': rectpack_settings.get('sort_algo', 0),  # 按面积排序
                'rotation_enabled': rectpack_settings.get('rotation_enabled', True),
                'optimization_level': rectpack_settings.get('optimization_level', 2),
                'max_iterations': rectpack_settings.get('max_iterations', 5)
            }

            # 根据图片数量调整参数
            if self.total_images > 100:
                optimized_params['optimization_level'] = 1  # 降低优化级别以提高速度
                optimized_params['max_iterations'] = 3
            elif self.total_images < 20:
                optimized_params['optimization_level'] = 3  # 提高优化级别以获得更好利用率
                optimized_params['max_iterations'] = 8

            # 应用优化参数
            if self.rectpack_arranger:
                # 使用RectPackArranger的set_algorithm_params方法
                self.rectpack_arranger.set_algorithm_params(
                    rotation_enabled=optimized_params.get('rotation_enabled', True),
                    sort_key=optimized_params.get('sort_algo', 0),
                    pack_algo=optimized_params.get('pack_algo', 0)
                )

            log.info(f"算法参数优化完成: {optimized_params}")
            self.status_changed.emit("算法参数优化完成")
            return optimized_params

        except Exception as e:
            error_msg = f"算法参数优化失败: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit(error_msg)
            return {}

    def process_images(self, pattern_items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], bool]:
        """处理图片列表，使用RectPack算法进行排列

        Args:
            pattern_items: 图片列表

        Returns:
            Tuple[List[Dict[str, Any]], bool]: (排列后的图片列表, 是否成功)
        """
        try:
            self.is_running = True
            self.start_time = time.time()
            self.total_images = len(pattern_items)
            self.processed_images = 0
            self.error_count = 0

            self.status_changed.emit(f"开始处理 {self.total_images} 张图片...")

            # 检查内存使用情况
            self.memory_manager.check_memory_usage()

            # 优化算法参数
            self.optimize_algorithm_parameters()

            # 使用统一排列器处理图片
            if not self.unified_arranger:
                raise Exception("统一排列器未初始化")

            arranged_images = self.unified_arranger.arrange_images(pattern_items)

            # 更新统计信息
            self.processed_images = len(arranged_images)
            self.last_utilization = self._calculate_utilization(arranged_images)

            # 发送完成信号
            self.progress_updated.emit(100)
            self.utilization_updated.emit(self.last_utilization)

            processing_time = time.time() - self.start_time
            self.status_changed.emit(f"图片处理完成，耗时 {processing_time:.2f} 秒，利用率 {self.last_utilization:.2f}%")

            log.info(f"RectPack算法处理完成: {self.processed_images}/{self.total_images} 张图片，利用率 {self.last_utilization:.2f}%")

            self.is_running = False
            return arranged_images, True

        except Exception as e:
            self.is_running = False
            error_msg = f"图片处理失败: {str(e)}"
            log.error(error_msg)
            self.error_occurred.emit(error_msg)
            return [], False

    def _calculate_utilization(self, arranged_images: List[Dict[str, Any]]) -> float:
        """计算画布利用率

        Args:
            arranged_images: 排列后的图片列表

        Returns:
            float: 利用率百分比
        """
        try:
            if not arranged_images or not self.rectpack_arranger:
                return 0.0

            layout_info = self.rectpack_arranger.get_layout_info()
            return layout_info.get('utilization_percent', 0.0)

        except Exception as e:
            log.error(f"计算利用率失败: {str(e)}")
            return 0.0

    def _handle_algorithm_log(self, message: str):
        """处理算法日志消息

        Args:
            message: 日志消息
        """
        # 解析进度信息
        if "进度:" in message:
            try:
                progress_str = message.split("进度:")[1].split("%")[0].strip()
                progress = int(float(progress_str))
                self.progress_updated.emit(progress)
            except:
                pass

        # 解析利用率信息
        if "利用率:" in message:
            try:
                util_str = message.split("利用率:")[1].split("%")[0].strip()
                utilization = float(util_str)
                self.utilization_updated.emit(utilization)
            except:
                pass

    def handle_error(self, error_msg: str) -> bool:
        """处理错误并尝试恢复

        Args:
            error_msg: 错误消息

        Returns:
            bool: 是否可以继续执行
        """
        self.error_count += 1
        log.error(f"RectPack错误 ({self.error_count}/{self.max_errors}): {error_msg}")

        if self.error_count >= self.max_errors:
            self.error_occurred.emit(f"错误次数过多，停止执行: {error_msg}")
            return False

        # 尝试恢复
        if self.retry_count < self.max_retries:
            self.retry_count += 1
            log.info(f"尝试恢复 ({self.retry_count}/{self.max_retries})")
            return True

        return False

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计数据
        """
        processing_time = time.time() - self.start_time if self.start_time else 0

        return {
            'total_images': self.total_images,
            'processed_images': self.processed_images,
            'processing_time': processing_time,
            'utilization': self.last_utilization,
            'error_count': self.error_count,
            'retry_count': self.retry_count,
            'images_per_second': self.processed_images / processing_time if processing_time > 0 else 0
        }

    def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            self.is_paused = False

            if self.rectpack_arranger:
                self.rectpack_arranger = None

            if self.unified_arranger:
                self.unified_arranger = None

            self.memory_manager.cleanup()
            log.info("RectPack管理器资源清理完成")

        except Exception as e:
            log.error(f"清理RectPack管理器资源失败: {str(e)}")
